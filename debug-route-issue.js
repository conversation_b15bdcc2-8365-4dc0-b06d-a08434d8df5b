#!/usr/bin/env node

/**
 * 调试路由创建问题
 * 1. 创建一个测试路由
 * 2. 获取新路由和 courses 路由的详细信息
 * 3. 对比两者的差异
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 简单的 MCP 客户端实现
class SimpleMCPClient {
  constructor() {
    this.serverProcess = null;
    this.requestId = 1;
  }

  async startServer() {
    console.log('🚀 启动 MCP 服务器...');
    
    this.serverProcess = spawn('node', [
      path.join(__dirname, 'dist', 'index.js'),
      '--base-url', 'https://app.dev.orb.local/api',
      '--token', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
      '--app', 'mcp_playground'
    ], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    // 等待服务器启动
    await new Promise(resolve => setTimeout(resolve, 2000));
    console.log('✅ MCP 服务器已启动');
  }

  async sendRequest(method, params = {}) {
    return new Promise((resolve, reject) => {
      if (!this.serverProcess) {
        reject(new Error('Server not started'));
        return;
      }

      const request = {
        jsonrpc: '2.0',
        id: this.requestId++,
        method: method,
        params: params
      };

      let responseData = '';
      const timeout = setTimeout(() => {
        reject(new Error('Request timeout'));
      }, 30000);

      const onData = (data) => {
        responseData += data.toString();
        
        const lines = responseData.trim().split('\n');
        for (const line of lines) {
          if (line.trim().startsWith('{')) {
            try {
              const response = JSON.parse(line);
              if (response.id === request.id) {
                clearTimeout(timeout);
                this.serverProcess.stdout.off('data', onData);
                
                if (response.error) {
                  reject(new Error(response.error.message || 'MCP Error'));
                } else {
                  resolve(response.result);
                }
                return;
              }
            } catch (e) {
              // 继续等待完整的响应
            }
          }
        }
      };

      this.serverProcess.stdout.on('data', onData);
      this.serverProcess.stdin.write(JSON.stringify(request) + '\n');
    });
  }

  async callTool(toolName, args = {}) {
    return this.sendRequest('tools/call', {
      name: toolName,
      arguments: args
    });
  }

  async close() {
    if (this.serverProcess) {
      this.serverProcess.kill();
      this.serverProcess = null;
    }
  }
}

// 调试路由问题
async function debugRouteIssue() {
  const client = new SimpleMCPClient();
  
  try {
    await client.startServer();
    
    // 初始化连接
    await client.sendRequest('initialize', {
      protocolVersion: '2024-11-05',
      capabilities: {},
      clientInfo: { name: 'route-debug-client', version: '1.0.0' }
    });
    
    console.log('\n🔍 === 调试路由创建问题 ===\n');
    
    // 步骤1: 获取现有路由列表
    console.log('📋 步骤1: 获取现有路由列表');
    try {
      const routesResult = await client.callTool('list_routes', { tree: true });
      console.log('✅ 路由列表获取成功');
      console.log(routesResult.content[0].text);
      
      // 查找 courses 路由
      const routesText = routesResult.content[0].text;
      const coursesMatch = routesText.match(/courses.*?ID:\s*(\d+)/i);
      const coursesId = coursesMatch ? coursesMatch[1] : null;
      console.log(`\n🎯 找到 courses 路由 ID: ${coursesId}`);
      
    } catch (error) {
      console.log('❌ 获取路由列表失败:', error.message);
    }
    
    // 步骤2: 创建测试路由
    console.log('\n➕ 步骤2: 创建测试路由');
    let testRouteId = null;
    try {
      const testRouteResult = await client.callTool('create_page_route', {
        title: '测试路由',
        icon: 'TestTubeOutlined',
        template: 'table',
        collectionName: 'users',
        enableTabs: true,
        hidden: false
      });
      
      console.log('✅ 测试路由创建成功');
      console.log(testRouteResult.content[0].text);
      
      // 提取路由ID
      const routeText = testRouteResult.content[0].text;
      const idMatch = routeText.match(/"id":\s*(\d+)/);
      testRouteId = idMatch ? idMatch[1] : null;
      console.log(`\n🆔 测试路由 ID: ${testRouteId}`);
      
    } catch (error) {
      console.log('❌ 创建测试路由失败:', error.message);
    }
    
    // 步骤3: 获取 courses 路由详细信息
    console.log('\n📖 步骤3: 获取 courses 路由详细信息');
    let coursesRouteData = null;
    try {
      // 先尝试已知的 courses 路由 ID
      const knownCoursesIds = [8, 9, 10, 11, 12]; // 可能的 courses 路由 ID
      
      for (const id of knownCoursesIds) {
        try {
          const coursesResult = await client.callTool('get_route', { id });
          const routeText = coursesResult.content[0].text;
          
          if (routeText.toLowerCase().includes('courses') || routeText.toLowerCase().includes('课程')) {
            console.log(`✅ 找到 courses 路由 (ID: ${id})`);
            console.log(coursesResult.content[0].text);
            coursesRouteData = JSON.parse(routeText.match(/\{[\s\S]*\}/)[0]);
            break;
          }
        } catch (e) {
          // 继续尝试下一个 ID
        }
      }
      
      if (!coursesRouteData) {
        console.log('❌ 未找到 courses 路由');
      }
      
    } catch (error) {
      console.log('❌ 获取 courses 路由失败:', error.message);
    }
    
    // 步骤4: 获取测试路由详细信息
    console.log('\n🧪 步骤4: 获取测试路由详细信息');
    let testRouteData = null;
    if (testRouteId) {
      try {
        const testResult = await client.callTool('get_route', { id: testRouteId });
        console.log('✅ 测试路由详细信息获取成功');
        console.log(testResult.content[0].text);
        
        const routeText = testResult.content[0].text;
        testRouteData = JSON.parse(routeText.match(/\{[\s\S]*\}/)[0]);
        
      } catch (error) {
        console.log('❌ 获取测试路由详细信息失败:', error.message);
      }
    }
    
    // 步骤5: 对比两个路由的差异
    console.log('\n🔍 步骤5: 对比路由差异');
    if (coursesRouteData && testRouteData) {
      console.log('\n=== COURSES 路由关键字段 ===');
      console.log(`ID: ${coursesRouteData.id}`);
      console.log(`Title: ${coursesRouteData.title}`);
      console.log(`Type: ${coursesRouteData.type}`);
      console.log(`SchemaUid: ${coursesRouteData.schemaUid}`);
      console.log(`MenuSchemaUid: ${coursesRouteData.menuSchemaUid}`);
      console.log(`EnableTabs: ${coursesRouteData.enableTabs}`);
      console.log(`Children: ${JSON.stringify(coursesRouteData.children, null, 2)}`);
      
      console.log('\n=== 测试路由关键字段 ===');
      console.log(`ID: ${testRouteData.id}`);
      console.log(`Title: ${testRouteData.title}`);
      console.log(`Type: ${testRouteData.type}`);
      console.log(`SchemaUid: ${testRouteData.schemaUid}`);
      console.log(`MenuSchemaUid: ${testRouteData.menuSchemaUid}`);
      console.log(`EnableTabs: ${testRouteData.enableTabs}`);
      console.log(`Children: ${JSON.stringify(testRouteData.children, null, 2)}`);
      
      console.log('\n=== 差异分析 ===');
      const differences = [];
      
      if (coursesRouteData.enableTabs !== testRouteData.enableTabs) {
        differences.push(`EnableTabs: courses=${coursesRouteData.enableTabs}, test=${testRouteData.enableTabs}`);
      }
      
      if (JSON.stringify(coursesRouteData.children) !== JSON.stringify(testRouteData.children)) {
        differences.push('Children 结构不同');
      }
      
      if (differences.length > 0) {
        console.log('🚨 发现差异:');
        differences.forEach(diff => console.log(`  - ${diff}`));
      } else {
        console.log('✅ 路由结构基本相同');
      }
    }
    
    // 步骤6: 获取 Schema 信息进行对比
    console.log('\n📄 步骤6: 获取 Schema 信息');
    if (coursesRouteData && testRouteData) {
      try {
        console.log('\n--- Courses Schema ---');
        const coursesSchemaResult = await client.callTool('get_schema', { 
          uid: coursesRouteData.schemaUid 
        });
        console.log(coursesSchemaResult.content[0].text);
        
        console.log('\n--- Test Route Schema ---');
        const testSchemaResult = await client.callTool('get_schema', { 
          uid: testRouteData.schemaUid 
        });
        console.log(testSchemaResult.content[0].text);
        
      } catch (error) {
        console.log('❌ 获取 Schema 信息失败:', error.message);
      }
    }
    
    console.log('\n🎉 调试完成！');
    
  } catch (error) {
    console.error('❌ 调试失败:', error.message);
  } finally {
    await client.close();
    console.log('\n🔚 MCP 服务器已关闭');
  }
}

// 运行脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  debugRouteIssue().catch(console.error);
}
