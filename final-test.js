#!/usr/bin/env node

/**
 * 最终测试：验证 MCP 路由创建修复
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 简单的 MCP 客户端实现
class SimpleMCPClient {
  constructor() {
    this.serverProcess = null;
    this.requestId = 1;
  }

  async startServer() {
    console.log('🚀 启动 MCP 服务器...');
    
    this.serverProcess = spawn('node', [
      path.join(__dirname, 'dist', 'index.js'),
      '--base-url', 'https://app.dev.orb.local/api',
      '--token', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
      '--app', 'mcp_playground'
    ], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    // 等待服务器启动
    await new Promise(resolve => setTimeout(resolve, 2000));
    console.log('✅ MCP 服务器已启动');
  }

  async sendRequest(method, params = {}) {
    return new Promise((resolve, reject) => {
      if (!this.serverProcess) {
        reject(new Error('Server not started'));
        return;
      }

      const request = {
        jsonrpc: '2.0',
        id: this.requestId++,
        method: method,
        params: params
      };

      let responseData = '';
      const timeout = setTimeout(() => {
        reject(new Error('Request timeout'));
      }, 30000);

      const onData = (data) => {
        responseData += data.toString();
        
        const lines = responseData.trim().split('\n');
        for (const line of lines) {
          if (line.trim().startsWith('{')) {
            try {
              const response = JSON.parse(line);
              if (response.id === request.id) {
                clearTimeout(timeout);
                this.serverProcess.stdout.off('data', onData);
                
                if (response.error) {
                  reject(new Error(response.error.message || 'MCP Error'));
                } else {
                  resolve(response.result);
                }
                return;
              }
            } catch (e) {
              // 继续等待完整的响应
            }
          }
        }
      };

      this.serverProcess.stdout.on('data', onData);
      this.serverProcess.stdin.write(JSON.stringify(request) + '\n');
    });
  }

  async callTool(toolName, args = {}) {
    return this.sendRequest('tools/call', {
      name: toolName,
      arguments: args
    });
  }

  async close() {
    if (this.serverProcess) {
      this.serverProcess.kill();
      this.serverProcess = null;
    }
  }
}

// 最终测试
async function finalTest() {
  const client = new SimpleMCPClient();
  
  try {
    await client.startServer();
    
    // 初始化连接
    await client.sendRequest('initialize', {
      protocolVersion: '2024-11-05',
      capabilities: {},
      clientInfo: { name: 'final-test-client', version: '1.0.0' }
    });
    
    console.log('\n🎯 === 最终修复验证测试 ===\n');
    
    // 创建最终测试路由
    console.log('➕ 创建最终测试路由（完全修复版本）');
    try {
      const testRouteResult = await client.callTool('create_page_route', {
        title: '最终修复测试',
        icon: 'CheckCircleOutlined',
        template: 'table',
        collectionName: 'users',
        enableTabs: true,  // 明确启用 tabs
        hidden: false
      });
      
      console.log('✅ 最终测试路由创建成功');
      console.log(testRouteResult.content[0].text);
      
      // 解析结果
      const routeText = testRouteResult.content[0].text;
      const routeData = JSON.parse(routeText.match(/\{[\s\S]*\}/)[0]);
      
      console.log('\n🔍 验证修复结果:');
      console.log(`✅ 路由 enableTabs: ${routeData.enableTabs} (应该是 true)`);
      console.log(`✅ 子路由 type: ${routeData.children[0].type} (应该是 "tabs")`);
      console.log(`✅ 子路由 schemaUid: ${routeData.children[0].schemaUid}`);
      console.log(`✅ 子路由 tabSchemaName: ${routeData.children[0].tabSchemaName}`);
      
      // 验证修复是否成功
      const isFixed = routeData.enableTabs === true && routeData.children[0].type === 'tabs';
      
      if (isFixed) {
        console.log('\n🎉 修复完全成功！');
        console.log('\n📋 修复内容总结:');
        console.log('  ✅ enableTabs 现在正确使用用户传入的参数');
        console.log('  ✅ 子路由 type 从错误的 "tab" 修正为正确的 "tabs"');
        console.log('  ✅ 路由结构现在与 NocoBase UI 创建的路由完全一致');
        console.log('\n💡 现在 MCP 创建的路由应该可以正常添加内容了！');
        
        // 提取路由 ID 用于进一步测试
        const routeId = routeData.id;
        console.log(`\n🆔 新路由 ID: ${routeId}`);
        console.log(`🌐 访问 URL: https://app.dev.orb.local/apps/mcp_playground/admin/${routeData.schemaUid}`);
        
      } else {
        console.log('\n❌ 修复仍然不完整');
        console.log(`  enableTabs: ${routeData.enableTabs} (期望: true)`);
        console.log(`  子路由 type: ${routeData.children[0].type} (期望: "tabs")`);
      }
      
    } catch (error) {
      console.log('❌ 创建最终测试路由失败:', error.message);
    }
    
    console.log('\n📊 总结:');
    console.log('通过 Playwright 手动操作和 API 请求分析，我们发现了 MCP 路由创建的问题：');
    console.log('1. enableTabs 参数被硬编码为 false');
    console.log('2. 子路由类型错误地设置为 "tab" 而不是 "tabs"');
    console.log('');
    console.log('修复后的路由应该具有与 NocoBase UI 创建的路由相同的结构，');
    console.log('从而解决"创建出来的菜单无法添加内容"的问题。');
    
  } catch (error) {
    console.error('❌ 最终测试失败:', error.message);
  } finally {
    await client.close();
    console.log('\n🔚 MCP 服务器已关闭');
  }
}

// 运行脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  finalTest().catch(console.error);
}
