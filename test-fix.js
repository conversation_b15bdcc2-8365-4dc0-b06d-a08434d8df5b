#!/usr/bin/env node

/**
 * 测试路由创建修复
 * 创建一个新的测试路由并验证结构是否正确
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 简单的 MCP 客户端实现
class SimpleMCPClient {
  constructor() {
    this.serverProcess = null;
    this.requestId = 1;
  }

  async startServer() {
    console.log('🚀 启动 MCP 服务器...');
    
    this.serverProcess = spawn('node', [
      path.join(__dirname, 'dist', 'index.js'),
      '--base-url', 'https://app.dev.orb.local/api',
      '--token', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
      '--app', 'mcp_playground'
    ], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    // 等待服务器启动
    await new Promise(resolve => setTimeout(resolve, 2000));
    console.log('✅ MCP 服务器已启动');
  }

  async sendRequest(method, params = {}) {
    return new Promise((resolve, reject) => {
      if (!this.serverProcess) {
        reject(new Error('Server not started'));
        return;
      }

      const request = {
        jsonrpc: '2.0',
        id: this.requestId++,
        method: method,
        params: params
      };

      let responseData = '';
      const timeout = setTimeout(() => {
        reject(new Error('Request timeout'));
      }, 30000);

      const onData = (data) => {
        responseData += data.toString();
        
        const lines = responseData.trim().split('\n');
        for (const line of lines) {
          if (line.trim().startsWith('{')) {
            try {
              const response = JSON.parse(line);
              if (response.id === request.id) {
                clearTimeout(timeout);
                this.serverProcess.stdout.off('data', onData);
                
                if (response.error) {
                  reject(new Error(response.error.message || 'MCP Error'));
                } else {
                  resolve(response.result);
                }
                return;
              }
            } catch (e) {
              // 继续等待完整的响应
            }
          }
        }
      };

      this.serverProcess.stdout.on('data', onData);
      this.serverProcess.stdin.write(JSON.stringify(request) + '\n');
    });
  }

  async callTool(toolName, args = {}) {
    return this.sendRequest('tools/call', {
      name: toolName,
      arguments: args
    });
  }

  async close() {
    if (this.serverProcess) {
      this.serverProcess.kill();
      this.serverProcess = null;
    }
  }
}

// 测试修复
async function testFix() {
  const client = new SimpleMCPClient();
  
  try {
    await client.startServer();
    
    // 初始化连接
    await client.sendRequest('initialize', {
      protocolVersion: '2024-11-05',
      capabilities: {},
      clientInfo: { name: 'fix-test-client', version: '1.0.0' }
    });
    
    console.log('\n🧪 === 测试路由创建修复 ===\n');
    
    // 创建测试路由（启用 tabs）
    console.log('➕ 创建测试路由（启用 tabs）');
    try {
      const testRouteResult = await client.callTool('create_page_route', {
        title: '修复测试路由',
        icon: 'BugOutlined',
        template: 'table',
        collectionName: 'users',
        enableTabs: true,  // 明确启用 tabs
        hidden: false
      });
      
      console.log('✅ 测试路由创建成功');
      console.log(testRouteResult.content[0].text);
      
      // 解析结果
      const routeText = testRouteResult.content[0].text;
      const routeData = JSON.parse(routeText.match(/\{[\s\S]*\}/)[0]);
      
      console.log('\n🔍 验证修复结果:');
      console.log(`✅ 路由 enableTabs: ${routeData.enableTabs} (应该是 true)`);
      console.log(`✅ 子路由 type: ${routeData.children[0].type} (应该是 "tabs")`);
      
      // 验证修复是否成功
      const isFixed = routeData.enableTabs === true && routeData.children[0].type === 'tabs';
      
      if (isFixed) {
        console.log('\n🎉 修复成功！路由结构现在与 courses 路由一致');
        console.log('\n📋 修复内容:');
        console.log('  ✅ enableTabs 现在使用用户传入的参数');
        console.log('  ✅ 子路由 type 从 "tab" 修正为 "tabs"');
        console.log('\n💡 现在创建的路由应该可以正常添加内容了');
      } else {
        console.log('\n❌ 修复失败，路由结构仍然不正确');
        console.log(`  enableTabs: ${routeData.enableTabs} (期望: true)`);
        console.log(`  子路由 type: ${routeData.children[0].type} (期望: "tabs")`);
      }
      
    } catch (error) {
      console.log('❌ 创建测试路由失败:', error.message);
    }
    
    // 对比 courses 路由
    console.log('\n📖 对比 courses 路由结构');
    try {
      const coursesResult = await client.callTool('get_route', { id: 1 });
      console.log('✅ courses 路由信息:');
      console.log(coursesResult.content[0].text);
      
      const coursesText = coursesResult.content[0].text;
      const coursesData = JSON.parse(coursesText.match(/\{[\s\S]*\}/)[0]);
      
      console.log('\n📊 courses 路由关键字段:');
      console.log(`  enableTabs: ${coursesData.enableTabs}`);
      console.log(`  子路由 type: ${coursesData.children[0].type}`);
      
    } catch (error) {
      console.log('❌ 获取 courses 路由失败:', error.message);
    }
    
    console.log('\n🎯 总结:');
    console.log('修复了 MCP 创建路由功能的两个关键问题:');
    console.log('1. enableTabs 参数现在正确传递给路由');
    console.log('2. 子路由类型从错误的 "tab" 修正为正确的 "tabs"');
    console.log('');
    console.log('这些修复应该解决了"创建出来的菜单无法添加内容"的问题。');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  } finally {
    await client.close();
    console.log('\n🔚 MCP 服务器已关闭');
  }
}

// 运行脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  testFix().catch(console.error);
}
